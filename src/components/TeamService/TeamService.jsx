import React from "react";
import { Swiper, SwiperSlide } from "swiper/react";
import "swiper/css";
import "swiper/css/autoplay";
import { Autoplay, Navigation } from "swiper/modules";
import { useRef } from "react";
import ArrowBackIosIcon from "@mui/icons-material/ArrowBackIos";
import ArrowForwardIosIcon from "@mui/icons-material/ArrowForwardIos";
import section5 from "../../assets/section5latest.jfif";
import team1 from "../../assets/teamImg.jpg";
import team2 from "../../assets/teamImg.jpg";
import team3 from "../../assets/teamImg.jpg";
// import startupIndia from "../../assets/startup_india.jpeg";
// import msme from "../../assets/msme.jpeg";
import { Link } from "react-router-dom";
const TeamService = () => {
  const prevRef = useRef(null);
  const nextRef = useRef(null);
  const teamMembers = [
    {
      id: 1,
      name: "<PERSON>",
      role: "Designer",
      image: team1,
    },
    {
      id: 2,
      name: "<PERSON>",
      role: "<PERSON><PERSON><PERSON>",
      image: team2,
    },
    {
      id: 3,
      name: "Charlie",
      role: "Project Manager",
      image: team3,
    },
    {
      id: 4,
      name: "Charlie",
      role: "Project Manager",
      image: team1,
    },
    // Add more team members as needed
  ];
  return (
    <div className="w-screen md:h-[100vh] sm:h-[150vh] flex bg-[#ea9368] sm:mt-0 mt-10">
      {/* Left Section */}
      <div className="hidden lg:flex lg:w-1/2 lg:justify-start lg:items-center">
        <img
          src={section5}
          alt="Team Service"
          className="w-[76%] h-[87%] rounded-r-[200px]"
        />
      </div>

      {/* Right Section */}
      <div className="md:w-1/2 flex flex-col items-start justify-center p-9 w-[100%] ">
        {/* Heading */}
        <div className="flex md:gap-4 gap-2 ">
          <h1 className=" font-Anton text-white tracking-wide text-2xl md:text-5xl font-bold mb-4  uppercase">
            About
          </h1>
          <h1 className=" font-Anton text-transparent font-outline-white-1 tracking-wide text-2xl md:text-5xl font-bold mb-4  uppercase">
            {" "}
            Urban Venue
          </h1>
        </div>

        {/* Paragraph */}
        <p className="font-archivo text-[15px] text-white font-normal leading-[25px] tracking-[1px]">
          Urban Venue provides top-tier event planning in Delhi NCR, offering
          curated venues, seamless execution, and transparent pricing.
        </p>
        <p className="font-archivo text-[15px] text-white font-normal leading-[25px] tracking-[1px]">
          Unlike direct farm bookings, we ensure personalized experiences,
          expert coordination, and cost protection—so you can enjoy your event
          while we handle the details.
        </p>
        <br />

        {/* <div className="flex md:gap-4 gap-2 ">
          <h3 className=" font-Anton text-white tracking-wide text-xl md:text-3xl  mb-4  uppercase">
            Most Trustable
          </h3>
          <h3 className=" font-Anton text-white tracking-wide text-xl md:text-3xl  mb-4  uppercase">
            Planner
          </h3>
          
        </div> */}


        {/* <div className="flex md:gap-4 gap-2 pb-10">
          <div className=" lg:flex lg:w-1/3 bg-white lg:justify-start lg:items-center">
            <img
              src={startupIndia}
              alt="Team Service"
              className=""
            />
          </div>

          <div className="lg:flex lg:w-2/6 bg-white lg:justify-start lg:items-center">
            <img
              src={msme}
              alt="Team Service"
              className=""
            />
          </div>
        </div> */}


        <Link to="/about">
          <button className="relative shadow-custom px-8 py-2 bg-white border border-black font-normal tracking-wide rounded-sm font-Anton transition-all duration-300 hover:scale-105 hover:shadow-xl hover:-translate-y-1 group ">
            READ MORE
          </button>
        </Link>

        <br />
        {/* 
        <div className="w-full  rounded-lg mt-4">
          <h1 className=" font-Anton text-white tracking-wide  text-4xl font-bold mb-4">
            {" "}
            THE TEAM
          </h1>

          <Swiper
            spaceBetween={20}
            slidesPerView={3}
            autoplay={{ delay: 3000, disableOnInteraction: false }}
            modules={[Autoplay, Navigation]}
            className="w-full "
            navigation={{
              prevEl: prevRef.current,
              nextEl: nextRef.current,
            }}
            onBeforeInit={(swiper) => {
              swiper.params.navigation.prevEl = prevRef.current;
              swiper.params.navigation.nextEl = nextRef.current;
            }}
          >
            {teamMembers.map((member) => (
              <SwiperSlide
                key={member.id}
                className="relative flex flex-col items-center group "
              >
                <img
                  src={member.image}
                  alt={member.name}
                  className="w-full aspect-square rounded-xl object-cover mb-4 overflow-hidden"
                />

                <div className="absolute inset-0 bg-black bg-opacity-50 rounded-xl flex flex-col h-55 items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity mb-4 duration-300">
                  <h2 className="text-white text-xl font-semibold">
                    {member.name}
                  </h2>
                  <p className="text-gray-300">{member.role}</p>
                </div>
              </SwiperSlide>
            ))}
          </Swiper>
          <br />

          <div className=" flex gap-5 items-center">
            <button
              ref={prevRef}
              className="  text-white rounded-full p-3  bg-white "
            >
              <ArrowBackIosIcon className="text-black ml-2" />
            </button>
            <button
              ref={nextRef}
              className=" text-white  rounded-full p-3   bg-white"
            >
              <ArrowForwardIosIcon className="text-black ml-2" />
            </button>
          </div>
        </div> */}
      </div>
    </div>
  );
};

export default TeamService;
