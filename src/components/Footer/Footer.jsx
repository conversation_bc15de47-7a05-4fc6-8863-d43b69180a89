import React from "react";
import {
  Facebook,
  Instagram,
  YouTube,
  WhatsApp,
  Pinterest,
} from "@mui/icons-material";
import Logo from "../../assets/uv_white_logo-removebg.png";
import { VelocityScroll } from "../ui/singleVelocity";
// import startupIndia from "../../assets/startup_india.jpeg";
// import msme from "../../assets/msme.jpeg";
// import badge from "../../assets/badge-weddingawards.jpg";
import { Link } from "react-router-dom";
const Footer = () => {
  return (
    <footer className=" text-white bg-[#2f2e2a] ">
      <div className="container mx-auto ">
        <div className="grid grid-cols-1 md:grid-cols-3 text-center md:text-left">
          {/* Menu Section */}
          <div className="flex flex-col justify-center items-center text-center border border-white p-8 px-24 border-l-transparent border-r-transparent ">
            <h6 className="text-5xl font-bold mb-2 font-Anton tracking-wide ">
              MENU
            </h6>
            <ul className="space-y-2">
              <Link to="/">
                {" "}
                <li className="text-lg font-serif relative group">
                  Home
                  <span className="absolute left-0 bottom-0 w-0 h-[2px] bg-red-500 transition-all duration-300 group-hover:w-full"></span>
                </li>
              </Link>
              <Link to="/events">
                <li className="text-lg font-serif relative group">
                  Events
                  <span className="absolute left-0 bottom-0 w-0 h-[2px] bg-red-500 transition-all duration-300 group-hover:w-full"></span>
                </li>
              </Link>
              <Link to="/venue">
                <li className="text-lg font-serif relative group">
                  Venues
                  <span className="absolute left-0 bottom-0 w-0 h-[2px] bg-red-500 transition-all duration-300 group-hover:w-full"></span>
                </li>
              </Link>
              <Link to="/contactus">
                <li className="text-lg font-serif relative group">
                  Contact
                  <span className="absolute left-0 bottom-0 w-0 h-[2px] bg-red-500 transition-all duration-300 group-hover:w-full"></span>
                </li>
              </Link>
              <Link to="https://urbanvenue.in/blog/" target="_blank">
                <li className="text-lg font-serif relative group">
                  Blogs
                  <span className="absolute left-0 bottom-0 w-0 h-[2px] bg-red-500 transition-all duration-300 group-hover:w-full"></span>
                </li>
              </Link>
            </ul>
          </div>

          {/* Logo and Social Icons */}
          <div className="flex flex-col justify-center items-center border border-white p-5">
            <img src={Logo} alt="Urban Venue Logo" className="w-[170px]" />
            <div className="flex space-x-4 mt-2 md:space-x-2 pt-10">
              <a
                href="https://www.facebook.com/people/Urban-Venue/61551117187653/"
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center justify-center w-10 h-10 rounded-full border border-[#1877F2] bg-transparent text-[#1877F2] transform transition duration-300 hover:bg-[#1877F2] hover:text-white hover:-translate-y-2"
              >
                <Facebook />
              </a>
              <a
                href="https://www.instagram.com/theurbanvenue/"
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center justify-center w-10 h-10 rounded-full border border-[#E1306C] bg-transparent text-[#E1306C] transform transition duration-300 hover:bg-[#E1306C] hover:text-white hover:-translate-y-2"
              >
                <Instagram />
              </a>
              <a
                href="https://www.youtube.com/@theurbanvenue/featured"
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center justify-center w-10 h-10 rounded-full border border-[#FF0000] bg-transparent text-[#FF0000] transform transition duration-300 hover:bg-[#FF0000] hover:text-white hover:-translate-y-2"
              >
                <YouTube />
              </a>
              <a
                href="https://wa.me/9871371364?text=Hi!%20I%20would%20like%20to%20connect."
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center justify-center w-10 h-10 rounded-full border border-[#25D366] bg-transparent text-[#25D366] transform transition duration-300 hover:bg-[#25D366] hover:text-white hover:-translate-y-2"
              >
                <WhatsApp />
              </a>
              <a
                href="https://in.pinterest.com/theurbanvenue/"
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center justify-center w-10 h-10 rounded-full border border-[#E60023] bg-transparent text-[#E60023] transform transition duration-300 hover:bg-[#E60023] hover:text-white hover:-translate-y-2"
              >
                <Pinterest />
              </a>
            </div>
          </div>

          {/* Info Section */}
          <div className="flex flex-col justify-center items-center text-center border border-white p-8 px-24 border-l-transparent border-r-transparent">
            <h6 className="text-5xl font-bold mb-2 font-Anton tracking-wide">
              INFO
            </h6>
            <ul className="space-y-2">
              <li className="text-lg font-serif relative group">
                Legal
                <span className="absolute left-0 bottom-0 w-0 h-[2px] bg-red-500 transition-all duration-300 group-hover:w-full"></span>
              </li>
              <li className="text-lg font-serif relative group">
                Confidentiality
                <span className="absolute left-0 bottom-0 w-0 h-[2px] bg-red-500 transition-all duration-300 group-hover:w-full"></span>
              </li>
              <li className="text-lg font-serif relative group">
                Privacy Policy
                <span className="absolute left-0 bottom-0 w-0 h-[2px] bg-red-500 transition-all duration-300 group-hover:w-full"></span>
              </li>
              <li className="text-lg font-serif relative group">
                Terms & Conditions
                <span className="absolute left-0 bottom-0 w-0 h-[2px] bg-red-500 transition-all duration-300 group-hover:w-full"></span>
              </li>
              <a href="/sitemap.xml" target="_blank" rel="noopener noreferrer">
                <li className="text-lg font-serif relative group">
                  Sitemap
                  <span className="absolute left-0 bottom-0 w-0 h-[2px] bg-red-500 transition-all duration-300 group-hover:w-full"></span>
                </li>
              </a>
            </ul>
          </div>
        </div>

        <div className="flex justify-center  py-2 border-0 md:gap-4 gap-2 pb-5 pt-5 border-b-2">
          
          {/* <div className=" lg:flex lg:w-1/6 bg-white lg:justify-start lg:items-center">
            <img
              src={startupIndia}
              alt="Team Service"
              className=""
            />
          </div>

          <div className="lg:flex lg:w-1/6 bg-white lg:justify-start lg:items-center">
            <img
              src={msme}
              alt="Team Service"
              className=""
            />
          </div> */}
          

          {/* <div className="lg:flex  lg:w-2/6 bg-white lg:justify-start lg:items-center">
            <img
              src={badge}
              alt="Team Service"
              className=""
            />
          </div> */}

        </div>
        {/* Copyright */}
        <div className="py-8 text-center text-gray-500 text-sm">
          © Copyright 2024-2025 <Link to={"https://owntechnologies.in/"} target="_blank" className="text-white text-lg"> owntechnologies.in</Link>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
